import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as s3n from 'aws-cdk-lib/aws-s3-notifications';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as cloudfront from 'aws-cdk-lib/aws-cloudfront';
import * as origins from 'aws-cdk-lib/aws-cloudfront-origins';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53targets from 'aws-cdk-lib/aws-route53-targets';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import { Construct } from 'constructs';

export interface StorageStackProps extends cdk.NestedStackProps {
  projectName: string;
  environment: string;
  isProductionOrStaging: boolean;
  mediaTable: dynamodb.Table;
  usersTable: dynamodb.Table;
  mediaDomainName?: string;
  certificateArn?: string;
}

export interface StorageStackOutputs {
  mediaBucket: s3.Bucket;
  bucketName: string;
  bucketArn: string;
  bucketDomainName: string;
  distribution?: cloudfront.Distribution;
  aiProcessingFunction: lambda.Function;
}

export class StorageStack extends cdk.NestedStack implements StorageStackOutputs {
  public readonly mediaBucket: s3.Bucket;
  public readonly bucketName: string;
  public readonly bucketArn: string;
  public readonly bucketDomainName: string;
  public readonly distribution?: cloudfront.Distribution;
  public readonly aiProcessingFunction: lambda.Function;

  constructor(scope: Construct, id: string, props: StorageStackProps) {
    super(scope, id, props);

    const { projectName, environment, isProductionOrStaging, mediaTable, usersTable, mediaDomainName, certificateArn } = props;

    // Create S3 bucket for media storage
    this.mediaBucket = this.createMediaBucket(projectName, environment, isProductionOrStaging);

    // Set public properties
    this.bucketName = this.mediaBucket.bucketName;
    this.bucketArn = this.mediaBucket.bucketArn;
    this.bucketDomainName = this.mediaBucket.bucketDomainName;

    // Create AI processing function
    this.aiProcessingFunction = this.createAiProcessingFunction(projectName, environment, mediaTable, usersTable);

    // Set up S3 event trigger for AI processing
    this.setupS3EventTrigger();

    // Create CloudFront distribution
    this.distribution = this.createCloudFrontDistribution(projectName, environment, isProductionOrStaging, mediaDomainName, certificateArn);

    // Create Route53 records if custom domain is provided
    if (mediaDomainName && this.distribution) {
      this.createRoute53Records(mediaDomainName, this.distribution);
    }

    // Create outputs
    this.createOutputs(mediaDomainName);
  }

  private createMediaBucket(
    projectName: string,
    environment: string,
    isProductionOrStaging: boolean
  ): s3.Bucket {
    const bucketName = `${projectName}-media-${environment}`;

    // Configure lifecycle rules
    const lifecycleRules: s3.LifecycleRule[] = [
      // Transition to Glacier after 30 days
      {
        id: 'glacier-transition',
        enabled: true,
        transitions: [
          {
            storageClass: s3.StorageClass.GLACIER,
            transitionAfter: cdk.Duration.days(30),
          },
        ],
      },
      // Clean up temp files after 1 day
      {
        id: 'cleanup-temp-files',
        enabled: true,
        prefix: 'temp/',
        expiration: cdk.Duration.days(1),
      },
      // Clean up processing files after 7 days
      {
        id: 'cleanup-processing-files',
        enabled: true,
        prefix: 'processing/',
        expiration: cdk.Duration.days(7),
      },
      // Clean up rejected content from staging after 1 day
      {
        id: 'cleanup-rejected-staging',
        enabled: true,
        prefix: 'staging/',
        expiration: cdk.Duration.days(1),
      },
      // Transition review content to Glacier after 90 days (for long-term storage)
      {
        id: 'review-content-glacier',
        enabled: true,
        prefix: 'review/',
        transitions: [
          {
            storageClass: s3.StorageClass.GLACIER,
            transitionAfter: cdk.Duration.days(90),
          },
        ],
      },
      // Clean up incomplete multipart uploads after 1 day
      {
        id: 'cleanup-incomplete-uploads',
        enabled: true,
        abortIncompleteMultipartUploadAfter: cdk.Duration.days(1),
      },
    ];

    // Add production-specific lifecycle rules
    if (isProductionOrStaging) {
      lifecycleRules.push({
        id: 'deep-archive-transition',
        enabled: true,
        transitions: [
          {
            storageClass: s3.StorageClass.DEEP_ARCHIVE,
            transitionAfter: cdk.Duration.days(365),
          },
        ],
      });
    }

    const bucket = new s3.Bucket(this, 'MediaBucket', {
      bucketName,
      versioned: isProductionOrStaging,
      publicReadAccess: false, // We'll use CloudFront for public access
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      encryption: s3.BucketEncryption.S3_MANAGED,
      lifecycleRules,
      removalPolicy: isProductionOrStaging ? cdk.RemovalPolicy.RETAIN : cdk.RemovalPolicy.DESTROY,
      autoDeleteObjects: !isProductionOrStaging,
      cors: [
        {
          allowedMethods: [
            s3.HttpMethods.GET,
            s3.HttpMethods.POST,
            s3.HttpMethods.PUT,
            s3.HttpMethods.DELETE,
            s3.HttpMethods.HEAD,
          ],
          allowedOrigins: ['*'], // Will be restricted via CloudFront
          allowedHeaders: ['*'],
          exposedHeaders: [
            'ETag',
            'x-amz-meta-custom-header',
          ],
          maxAge: 3000,
        },
      ],
      // Inventory configuration will be added separately if needed
      // Intelligent tiering will be configured separately if needed
    });

    // Bucket notification configuration will be added by AI processing stack

    return bucket;
  }

  private createOutputs(mediaDomainName?: string): void {
    new cdk.CfnOutput(this, 'MediaBucketName', {
      value: this.bucketName,
      description: 'Name of the S3 media bucket',
      exportName: `${this.stackName}-MediaBucketName`,
    });

    new cdk.CfnOutput(this, 'MediaBucketArn', {
      value: this.bucketArn,
      description: 'ARN of the S3 media bucket',
      exportName: `${this.stackName}-MediaBucketArn`,
    });

    new cdk.CfnOutput(this, 'MediaBucketDomainName', {
      value: this.bucketDomainName,
      description: 'Domain name of the S3 media bucket',
      exportName: `${this.stackName}-MediaBucketDomainName`,
    });

    // CloudFront distribution outputs
    if (this.distribution) {
      new cdk.CfnOutput(this, 'DistributionId', {
        value: this.distribution.distributionId,
        description: 'CloudFront Distribution ID',
        exportName: `${this.stackName}-DistributionId`,
      });

      new cdk.CfnOutput(this, 'DistributionDomainName', {
        value: this.distribution.distributionDomainName,
        description: 'CloudFront Distribution Domain Name',
        exportName: `${this.stackName}-DistributionDomainName`,
      });

      // Custom domain outputs if configured
      if (mediaDomainName) {
        new cdk.CfnOutput(this, 'MediaCustomDomain', {
          value: mediaDomainName,
          description: 'Custom domain name for media CDN',
          exportName: `${this.stackName}-MediaCustomDomain`,
        });

        new cdk.CfnOutput(this, 'MediaUrl', {
          value: `https://${mediaDomainName}`,
          description: 'Media CDN URL with custom domain',
          exportName: `${this.stackName}-MediaUrl`,
        });
      }
    }
  }

  private createCloudFrontDistribution(
    projectName: string,
    environment: string,
    isProductionOrStaging: boolean,
    mediaDomainName?: string,
    certificateArn?: string
  ): cloudfront.Distribution {
    // Create Origin Access Control for S3
    const originAccessControl = new cloudfront.S3OriginAccessControl(this, 'MediaOAC', {
      description: `OAC for ${projectName} media bucket in ${environment}`,
    });

    // Create S3 origin
    const s3Origin = origins.S3BucketOrigin.withOriginAccessControl(this.mediaBucket, {
      originAccessControl,
    });

    // Prepare custom domain configuration
    let domainNames: string[] | undefined;
    let certificate: acm.ICertificate | undefined;

    if (mediaDomainName && certificateArn) {
      domainNames = [mediaDomainName];
      certificate = acm.Certificate.fromCertificateArn(this, 'MediaCertificate', certificateArn);
    }

    // Create CloudFront distribution
    const distribution = new cloudfront.Distribution(this, 'MediaDistribution', {
      defaultBehavior: {
        origin: s3Origin,
        viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
        allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
        cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
        compress: true,
        cachePolicy: cloudfront.CachePolicy.CACHING_OPTIMIZED,
        originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
        responseHeadersPolicy: cloudfront.ResponseHeadersPolicy.CORS_ALLOW_ALL_ORIGINS_WITH_PREFLIGHT_AND_SECURITY_HEADERS,
      },
      additionalBehaviors: {
        // Block access to staging area (no caching)
        '/staging/*': {
          origin: s3Origin,
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
          compress: false,
          cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
          originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
          responseHeadersPolicy: cloudfront.ResponseHeadersPolicy.CORS_ALLOW_ALL_ORIGINS_WITH_PREFLIGHT_AND_SECURITY_HEADERS,
        },
        // Block access to review area
        '/review/*': {
          origin: s3Origin,
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.REDIRECT_TO_HTTPS,
          allowedMethods: cloudfront.AllowedMethods.ALLOW_GET_HEAD_OPTIONS,
          cachedMethods: cloudfront.CachedMethods.CACHE_GET_HEAD_OPTIONS,
          compress: false,
          cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
          originRequestPolicy: cloudfront.OriginRequestPolicy.CORS_S3_ORIGIN,
          responseHeadersPolicy: cloudfront.ResponseHeadersPolicy.CORS_ALLOW_ALL_ORIGINS_WITH_PREFLIGHT_AND_SECURITY_HEADERS,
        },
      },
      comment: `${projectName} media CDN for ${environment}`,
      enabled: true,
      httpVersion: cloudfront.HttpVersion.HTTP2_AND_3,
      priceClass: isProductionOrStaging ? cloudfront.PriceClass.PRICE_CLASS_ALL : cloudfront.PriceClass.PRICE_CLASS_100,
      domainNames,
      certificate,
    });

    // Grant CloudFront access to S3 bucket
    const bucketPolicy = new iam.PolicyStatement({
      effect: iam.Effect.ALLOW,
      principals: [new iam.ServicePrincipal('cloudfront.amazonaws.com')],
      actions: ['s3:GetObject'],
      resources: [`${this.mediaBucket.bucketArn}/*`],
      conditions: {
        StringEquals: {
          'AWS:SourceArn': `arn:aws:cloudfront::${this.account}:distribution/${distribution.distributionId}`,
        },
      },
    });

    this.mediaBucket.addToResourcePolicy(bucketPolicy);

    return distribution;
  }

  private createRoute53Records(
    mediaDomainName: string,
    distribution: cloudfront.Distribution
  ): void {
    // Extract the root domain from the media domain (e.g., "gameflex.io" from "dev.media.gameflex.io")
    const domainParts = mediaDomainName.split('.');
    const rootDomain = domainParts.slice(-2).join('.'); // Gets "gameflex.io"

    // Import the existing hosted zone
    const hostedZone = route53.HostedZone.fromLookup(this, 'MediaHostedZone', {
      domainName: rootDomain,
    });

    // Create A record pointing to CloudFront distribution
    new route53.ARecord(this, 'MediaARecord', {
      zone: hostedZone,
      recordName: mediaDomainName,
      target: route53.RecordTarget.fromAlias(new route53targets.CloudFrontTarget(distribution)),
      comment: `Media CDN alias for ${mediaDomainName}`,
    });

    // Create AAAA record for IPv6 support
    new route53.AaaaRecord(this, 'MediaAAAARecord', {
      zone: hostedZone,
      recordName: mediaDomainName,
      target: route53.RecordTarget.fromAlias(new route53targets.CloudFrontTarget(distribution)),
      comment: `Media CDN IPv6 alias for ${mediaDomainName}`,
    });
  }

  private createAiProcessingFunction(
    projectName: string,
    environment: string,
    mediaTable: dynamodb.Table,
    usersTable: dynamodb.Table
  ): lambda.Function {
    const functionName = `${projectName}-ai-processing-${environment}`;

    const aiFunction = new lambda.Function(this, 'AiProcessingFunction', {
      functionName,
      runtime: lambda.Runtime.NODEJS_20_X,
      handler: 'index.handler',
      code: lambda.Code.fromAsset('src/ai-processing'),
      timeout: cdk.Duration.minutes(5), // AI processing can take time
      memorySize: 1024, // More memory for image processing
      environment: {
        ENVIRONMENT: environment,
        MEDIA_TABLE: mediaTable.tableName,
        USERS_TABLE: usersTable.tableName,
        BUCKET_NAME: this.mediaBucket.bucketName,
      },
      // Note: Reserved concurrent executions removed for development to avoid account limits
    });

    // Grant permissions to the AI processing function
    mediaTable.grantReadWriteData(aiFunction);
    usersTable.grantReadWriteData(aiFunction);
    this.mediaBucket.grantReadWrite(aiFunction);

    // Grant comprehensive Rekognition permissions
    aiFunction.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'rekognition:DetectModerationLabels',
          'rekognition:DetectLabels',
          'rekognition:DetectText',
          'rekognition:DetectFaces',
          'rekognition:RecognizeCelebrities',
          'rekognition:DetectProtectiveEquipment',
          'rekognition:GetLabelDetection',
          'rekognition:GetContentModeration',
        ],
        resources: ['*'],
      })
    );

    // Grant CloudWatch Logs permissions
    aiFunction.addToRolePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        actions: [
          'logs:CreateLogGroup',
          'logs:CreateLogStream',
          'logs:PutLogEvents',
        ],
        resources: [
          `arn:aws:logs:${cdk.Stack.of(this).region}:${cdk.Stack.of(this).account}:log-group:/aws/lambda/${functionName}*`,
        ],
      })
    );

    return aiFunction;
  }

  private setupS3EventTrigger(): void {
    // Add S3 event notification to trigger AI processing when objects are created in staging
    // Only process files uploaded to the staging area
    this.mediaBucket.addEventNotification(
      s3.EventType.OBJECT_CREATED,
      new s3n.LambdaDestination(this.aiProcessingFunction),
      {
        prefix: 'staging/', // Only trigger for staging uploads
      }
    );

    console.log('AI Processing Lambda will be triggered for uploads to staging/ prefix only');
  }
}
